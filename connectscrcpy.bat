@echo off
setlocal

:: Add "silent" as first parameter to run without console output: connect-scrcpy.bat silent
if "%1"=="silent" goto SILENT_MODE

:: ==========================================================================
:: 🔥 SCRCPY SMART CONNECTOR 🔥
:: 
:: Automatically connects to Android device via:
:: 📱 Wireless (Hotspot) - PRIORITY
:: 🔌 USB Cable - FALLBACK
:: ==========================================================================

cls
echo.
echo ================================================================
echo                   SCRCPY SMART CONNECTOR                    
echo                                                              
echo   Wireless Priority  -  USB Fallback  -  Auto-Detection    
echo ================================================================
echo.


:: --- 1. Configuration ---
:: Set the full path to the folder containing adb.exe and scrcpy.exe.
:: IMPORTANT: You must update this path to match your system.
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"

:: Temporarily add the scrcpy folder to the system PATH for this session.
:: This allows calling 'adb.exe' and 'scrcpy.exe' without the full path.
set "PATH=%SCRCPY_PATH%;%PATH%"

goto NORMAL_MODE

:SILENT_MODE
:: Silent mode - redirect all script execution without console output
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"
call :MAIN_LOGIC >nul 2>&1
exit /b

:NORMAL_MODE

:MAIN_LOGIC
cls
echo.
echo ================================================================
echo                   SCRCPY SMART CONNECTOR
echo
echo   Choose your connection method:
echo ================================================================
echo.
echo   [1] Auto-Connect (Wireless Priority, USB Fallback)
echo   [2] Manual CLI Mode (for adb pair, adb connect, etc.)
echo   [3] Exit
echo.
set /p "choice=Enter your choice (1-3): "

if "%choice%"=="1" goto AUTO_CONNECT
if "%choice%"=="2" goto MANUAL_CLI
if "%choice%"=="3" goto END
echo Invalid choice. Please enter 1, 2, or 3.
pause
goto MAIN_LOGIC

:AUTO_CONNECT
cls
echo.
echo ================================================================
echo                   AUTO-CONNECT MODE
echo
echo   Wireless Priority  -  USB Fallback  -  Auto-Detection
echo ================================================================
echo.

:: --- WIRELESS DETECTION ---
echo [*] Scanning for phone's hotspot...

:: Show what we're checking
ipconfig | findstr /i "Default Gateway" > nul
if errorlevel 1 (
    echo    [X] No network gateway detected
    goto USB_MODE
)

:: Clear the variable first
set "PHONE_IP="

:: Extract the hotspot IP
for /f "tokens=2 delims=:" %%G in ('ipconfig ^| findstr /i "Default Gateway"') do (
    set "PHONE_IP=%%G"
    goto IP_CHECK
)

:IP_CHECK
:: Clean up the IP
if defined PHONE_IP set "PHONE_IP=%PHONE_IP: =%"

:: Validate IP format
if defined PHONE_IP (
    echo %PHONE_IP% | findstr /c:"." > nul
    if not errorlevel 1 (
        echo    [OK] Hotspot IP found: %PHONE_IP%
        goto WIRELESS_SETUP
    )
)

:: No valid IP found
echo    [X] No hotspot IP detected
goto USB_MODE

:WIRELESS_SETUP
echo.
echo [*] Setting up wireless connection...
echo    [*] Enabling ADB TCP/IP mode...

:: Enable wireless mode
adb tcpip 5555

echo    [*] Connecting to %PHONE_IP%:5555...
adb connect %PHONE_IP%:5555

:: Test connection
timeout /t 2 /nobreak > nul
adb -s %PHONE_IP%:5555 get-state 1>nul 2>nul
if errorlevel 1 (
    echo    [X] Wireless connection failed
    echo    [*] Switching to USB...
    goto USB_MODE
)

echo    [OK] Wireless connection successful!
echo.
echo [*] Launching scrcpy wirelessly...
scrcpy -s %PHONE_IP%:5555
goto END

:: --- USB FALLBACK MODE ---
:USB_MODE
echo.
echo [*] Checking for USB connection...

:: Check for USB device
adb -d get-state 1>nul 2>nul
if errorlevel 1 (
    echo.
    echo [X] No device found!
    echo.
    echo [!] Please ensure:
    echo     * Phone connected via USB with debugging enabled, OR
    echo     * PC connected to phone's hotspot
    echo.
    pause
    exit /b
)

echo    [OK] USB device detected!
echo.
echo [*] Launching scrcpy via USB...
scrcpy -d
goto END

:: --- MANUAL CLI MODE ---
:MANUAL_CLI
cls
echo.
echo ================================================================
echo                     MANUAL CLI MODE
echo
echo   Direct access to ADB and SCRCPY commands
echo ================================================================
echo.
echo [!] MANUAL CLI MODE INSTRUCTIONS:
echo.
echo For WIRELESS PAIRING (Android 11+):
echo   1. On your phone: Settings ^> Developer options ^> Wireless debugging
echo   2. Tap "Pair device with pairing code"
echo   3. Note the IP address and pairing port (6-digit)
echo   4. Run: adb pair ^<IP^>:^<pairing_port^>
echo   5. Enter the pairing code from your phone
echo.
echo For WIRELESS CONNECTION (after pairing):
echo   1. On your phone: Settings ^> Developer options ^> Wireless debugging
echo   2. Note the IP address and connection port (usually 5555)
echo   3. Run: adb connect ^<IP^>:^<connection_port^>
echo   4. Run: scrcpy
echo.
echo For USB CONNECTION:
echo   1. Connect phone via USB with debugging enabled
echo   2. Run: adb devices (to verify connection)
echo   3. Run: scrcpy
echo.
echo COMMON COMMANDS:
echo   adb devices          - List connected devices
echo   adb pair ^<IP^>:^<port^>  - Pair with device wirelessly
echo   adb connect ^<IP^>:^<port^> - Connect to device wirelessly
echo   adb disconnect       - Disconnect wireless connection
echo   scrcpy               - Launch screen mirroring
echo   scrcpy --help        - Show all scrcpy options
echo.
echo [*] You are now in the scrcpy directory with ADB and SCRCPY in PATH
echo [*] Type 'exit' to return to the main menu
echo.

:: Keep the command prompt open in the scrcpy directory
cmd /k "echo Manual CLI Mode - Ready for ADB/SCRCPY commands"

goto MAIN_LOGIC

:END
echo.
echo ================================================================
echo                        TASK COMPLETE                        
echo ================================================================
pause
return

endlocal