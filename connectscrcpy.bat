@echo off
setlocal

:: Add "silent" as first parameter to run without console output: connect-scrcpy.bat silent
if "%1"=="silent" goto SILENT_MODE

:: ==========================================================================
:: 🔥 SCRCPY SMART CONNECTOR 🔥
::
:: Connection options:
:: 🤖 Auto-Connect: Wireless (Hotspot) Priority, USB Fallback
:: �️ Manual CLI: Direct access for adb pair, adb connect, etc.
:: � Supports wireless pairing for Android 11+
:: ==========================================================================

cls
echo.
echo ================================================================
echo                   SCRCPY SMART CONNECTOR                    
echo                                                              
echo   Wireless Priority  -  USB Fallback  -  Auto-Detection    
echo ================================================================
echo.


:: --- 1. Configuration ---
:: Set the full path to the folder containing adb.exe and scrcpy.exe.
:: IMPORTANT: You must update this path to match your system.
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"

:: Temporarily add the scrcpy folder to the system PATH for this session.
:: This allows calling 'adb.exe' and 'scrcpy.exe' without the full path.
set "PATH=%SCRCPY_PATH%;%PATH%"

goto NORMAL_MODE

:SILENT_MODE
:: Silent mode - redirect all script execution without console output
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"
set "SILENT_MODE_ACTIVE=1"
call :MAIN_LOGIC >nul 2>&1
exit /b

:NORMAL_MODE

:MAIN_LOGIC
cls
echo.
echo ================================================================
echo                   SCRCPY SMART CONNECTOR
echo
echo   Wireless Priority  -  USB Fallback  -  Manual CLI
echo ================================================================
echo.

:: --- WIRELESS DETECTION ---
echo [*] Scanning for phone's hotspot...

:: Show what we're checking
ipconfig | findstr /i "Default Gateway" > nul
if errorlevel 1 (
    echo    [X] No network gateway detected
    goto USB_MODE
)

:: Clear the variable first
set "PHONE_IP="

:: Extract the hotspot IP
for /f "tokens=2 delims=:" %%G in ('ipconfig ^| findstr /i "Default Gateway"') do (
    set "PHONE_IP=%%G"
    goto IP_CHECK
)

:IP_CHECK
:: Clean up the IP
if defined PHONE_IP set "PHONE_IP=%PHONE_IP: =%"

:: Validate IP format
if defined PHONE_IP (
    echo %PHONE_IP% | findstr /c:"." > nul
    if not errorlevel 1 (
        echo    [OK] Hotspot IP found: %PHONE_IP%
        goto WIRELESS_SETUP
    )
)

:: No valid IP found
echo    [X] No hotspot IP detected
goto USB_MODE

:WIRELESS_SETUP
echo.
echo [*] Setting up wireless connection...
echo    [*] Enabling ADB TCP/IP mode...

:: Enable wireless mode
adb tcpip 5555

echo    [*] Connecting to %PHONE_IP%:5555...
adb connect %PHONE_IP%:5555

:: Test connection
timeout /t 2 /nobreak > nul
adb -s %PHONE_IP%:5555 get-state 1>nul 2>nul
if errorlevel 1 (
    echo    [X] Wireless connection failed
    echo    [*] Switching to USB...
    goto USB_MODE
)

echo    [OK] Wireless connection successful!
echo.
echo [*] Launching scrcpy wirelessly...
scrcpy -s %PHONE_IP%:5555
goto SUCCESS_COUNTDOWN

:: --- USB FALLBACK MODE ---
:USB_MODE
echo.
echo [*] Checking for USB connection...

:: Check for USB device
adb -d get-state 1>nul 2>nul
if errorlevel 1 (
    echo.
    echo [X] No USB device found!
    echo.
    echo [!] Both wireless and USB connections failed.
    echo [*] Switching to Manual CLI mode for wireless debugging...
    echo.
    if not defined SILENT_MODE_ACTIVE pause
    goto MANUAL_CLI
)

echo    [OK] USB device detected!
echo.
echo [*] Launching scrcpy via USB...
scrcpy -d
goto SUCCESS_COUNTDOWN

:: --- MANUAL CLI MODE ---
:MANUAL_CLI
cls
echo.
echo ================================================================
echo                     MANUAL CLI MODE
echo
echo   Wireless Debugging on Shared WiFi Network
echo ================================================================
echo.
echo [!] WIRELESS DEBUGGING SETUP (Both devices on same WiFi):
echo.
echo STEP 1 - ENABLE WIRELESS DEBUGGING:
echo   * On your phone: Settings ^> Developer options ^> Wireless debugging
echo   * Turn ON "Wireless debugging"
echo.
echo STEP 2 - PAIR YOUR DEVICE (First time only):
echo   * Tap "Pair device with pairing code"
echo   * Note the IP address and pairing port (6-digit number)
echo   * Run: adb pair ^<IP^>:^<pairing_port^>
echo   * Enter the pairing code shown on your phone
echo.
echo STEP 3 - CONNECT TO YOUR DEVICE:
echo   * Go back to "Wireless debugging" main screen
echo   * Note the IP address and port (usually 5555)
echo   * Run: adb connect ^<IP^>:^<port^>
echo.
echo STEP 4 - START SCRCPY:
echo   * Run: scrcpy
echo.
echo USEFUL COMMANDS:
echo   adb devices          - List connected devices
echo   adb disconnect       - Disconnect current connection
echo   scrcpy               - Launch screen mirroring
echo   scrcpy -e            - Use TCP/IP device (wireless)
echo   scrcpy -d            - Use USB device
echo   scrcpy -s ^<device^>    - Use specific device
echo   scrcpy --help        - Show all scrcpy options
echo   autoclose            - Close window after successful connection
echo.
echo [*] Ready for manual commands in scrcpy directory
echo [*] Type 'exit' to close this session
echo [*] Type 'autoclose' after running scrcpy to auto-close window
echo.

:: Keep the command prompt open in the scrcpy directory (unless in silent mode)
if defined SILENT_MODE_ACTIVE (
    echo Silent mode: Manual CLI would open here, but skipping interactive prompt
    exit /b
) else (
    cmd /k
)

:SUCCESS_COUNTDOWN
echo.
echo ================================================================
echo                     CONNECTION SUCCESSFUL!
echo ================================================================
echo.
echo [*] SCRCPY is now running in the background
echo [*] Console window will auto-close in:
echo.

:: Countdown from 5 to 1
for /l %%i in (5,-1,1) do (
    echo                          %%i seconds...
    timeout /t 1 /nobreak >nul
)

echo.
echo [*] Closing console window...
timeout /t 1 /nobreak >nul
exit

:END
echo.
echo ================================================================
echo                        TASK COMPLETE
echo ================================================================
if not defined SILENT_MODE_ACTIVE pause
return

endlocal