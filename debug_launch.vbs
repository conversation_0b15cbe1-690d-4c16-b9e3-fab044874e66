Set objShell = CreateObject("WScript.Shell")
Set objArgs = WScript.Arguments

' Build the command line
strCommand = """C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1\scrcpy.exe"""

' Add arguments if provided
If objArgs.Count > 0 Then
    For i = 0 To objArgs.Count - 1
        strCommand = strCommand & " " & objArgs(i)
    Next
End If

' Show what command we're trying to run
WScript.Echo "Debug: Command to run: " & strCommand

' Try to run the command with visible window first for debugging
On Error Resume Next
intReturn = objShell.Run(strCommand, 1, False)
If Err.Number <> 0 Then
    WScript.Echo "Error running command: " & Err.Description
Else
    WScript.Echo "Command executed successfully, return code: " & intReturn
End If
