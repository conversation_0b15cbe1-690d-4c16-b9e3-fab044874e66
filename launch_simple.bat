@echo off
:: Simple silent launcher using start with /MIN to minimize console
:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

echo [*] Launching SCRCPY with minimized console...

:: Use start /MIN to minimize the console window instead of hiding it completely
if "%*"=="" (
    start /MIN "SCRCPY" scrcpy.exe
) else (
    start /MIN "SCRCPY" scrcpy.exe %*
)

echo [*] SCRCPY launched with minimized console window
echo [*] Phone screen should appear shortly
echo [*] You can now use 'autoclose' to close this window
