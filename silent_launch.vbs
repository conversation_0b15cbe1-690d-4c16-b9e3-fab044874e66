Set objShell = CreateObject("WScript.Shell")
Set objArgs = WScript.Arguments

' Build the command line
strCommand = """C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1\scrcpy.exe"""

' Add arguments if provided
If objArgs.Count > 0 Then
    For i = 0 To objArgs.Count - 1
        strCommand = strCommand & " " & objArgs(i)
    Next
End If

' Run the command with hidden window (0 = hidden, False = don't wait)
objShell.Run strCommand, 0, False
