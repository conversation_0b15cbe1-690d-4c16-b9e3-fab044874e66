Set objShell = CreateObject("WScript.Shell")
Set objArgs = WScript.Arguments

' Build the command line with cmd wrapper to hide only console
strCommand = "cmd /c """ & """C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1\scrcpy.exe"""

' Add arguments if provided
If objArgs.Count > 0 Then
    For i = 0 To objArgs.Count - 1
        strCommand = strCommand & " " & objArgs(i)
    Next
End If

strCommand = strCommand & """"

' Run with hidden console (0 = hidden) but allow GUI windows to show
objShell.Run strCommand, 0, False
