@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Test if scrcpy is accessible first
echo [*] Testing SCRCPY accessibility...
scrcpy --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] SCRCPY not found in PATH
    echo [*] Trying direct path...
    "%SCRCPY_PATH%\scrcpy.exe" --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] SCRCPY not found at: %SCRCPY_PATH%
        echo [*] Please check the path in the script
        pause
        exit /b
    )
)

:: Use PowerShell to launch <PERSON><PERSON><PERSON><PERSON> in completely detached process
echo [*] Launching SCRCPY in detached process...

if "%*"=="" (
    powershell -Command "Start-Process -FilePath '%SCRCPY_PATH%\scrcpy.exe' -WorkingDirectory '%SCRCPY_PATH%'"
) else (
    powershell -Command "Start-Process -FilePath '%SCRCPY_PATH%\scrcpy.exe' -ArgumentList '%*' -WorkingDirectory '%SCRCPY_PATH%'"
)

echo [*] SCRCPY launched successfully
echo [*] Phone screen should appear shortly
echo [*] Console is now free for commands
echo [*] You can now use 'autoclose' to close this window
