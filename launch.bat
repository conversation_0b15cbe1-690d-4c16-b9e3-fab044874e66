@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Launch SCRCPY silently using start /B to run without console window
if "%*"=="" (
    start /B "" scrcpy.exe >nul 2>&1
) else (
    start /B "" scrcpy.exe %* >nul 2>&1
)

echo [*] SCRCPY launched silently in background
echo [*] Phone screen should appear shortly (no console window)
echo [*] You can now use 'autoclose' to close this window
