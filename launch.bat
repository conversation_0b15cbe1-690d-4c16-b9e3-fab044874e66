@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Test if scrcpy is accessible first
echo [*] Testing SCRCPY accessibility...
scrcpy --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] SCRCPY not found in PATH
    echo [*] Trying direct path...
    "%SCRCPY_PATH%\scrcpy.exe" --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] SCRCPY not found at: %SCRCPY_PATH%
        echo [*] Please check the path in the script
        pause
        exit /b
    )
)

:: Launch SCRCPY completely silently using VBScript (no console window at all)
echo [*] Launching SCRCP<PERSON> silently...
cscript //nologo silent_launch.vbs %*

echo [*] SCRCPY command sent to background
echo [*] Phone screen should appear shortly (NO console window)
echo [*] If no phone screen appears, try 'scrcpy %*' to see error messages
echo [*] You can now use 'autoclose' to close this window
