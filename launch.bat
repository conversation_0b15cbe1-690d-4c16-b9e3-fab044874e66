@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Launch SCRCPY silently using PowerShell to hide console window
powershell -WindowStyle Hidden -Command "Start-Process 'scrcpy.exe' -ArgumentList '%*' -WindowStyle Normal"

echo [*] SCRCPY launched silently in background
echo [*] Phone screen should appear shortly
echo [*] You can now use 'autoclose' to close this window
