@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Test if scrcpy is accessible first
echo [*] Testing SCRCPY accessibility...
scrcpy --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] SCRCPY not found in PATH
    echo [*] Trying direct path...
    "%SCRCPY_PATH%\scrcpy.exe" --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] SCRCPY not found at: %SCRCPY_PATH%
        echo [*] Please check the path in the script
        pause
        exit /b
    )
)

:: Create a temporary batch file that will launch SCRCP<PERSON> and self-destruct
echo [*] Creating temporary launcher...
set "TEMP_LAUNCHER=%TEMP%\scrcpy_temp_%RANDOM%.bat"

:: Write the temporary launcher
echo @echo off > "%TEMP_LAUNCHER%"
echo cd /d "%SCRCPY_PATH%" >> "%TEMP_LAUNCHER%"
if "%*"=="" (
    echo scrcpy.exe >> "%TEMP_LAUNCHER%"
) else (
    echo scrcpy.exe %* >> "%TEMP_LAUNCHER%"
)
echo del "%%~f0" >> "%TEMP_LAUNCHER%"

:: Launch the temporary batch file in a new window and return immediately
start "" "%TEMP_LAUNCHER%"

echo [*] SCRCPY launched in separate window
echo [*] Phone screen should appear shortly
echo [*] This console is now free for commands
echo [*] You can now use 'autoclose' to close this window
