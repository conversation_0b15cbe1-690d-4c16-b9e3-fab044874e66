@echo off
:: Silent SCRCPY launcher - runs SCRCPY without console window
:: Usage: launch.bat [scrcpy arguments]
:: Examples: 
::   launch.bat -e     (for wireless)
::   launch.bat -d     (for USB)
::   launch.bat        (auto-detect)

:: Set the scrcpy path (same as main script)
set "SCRCPY_PATH=C:\Users\<USER>\Downloads\Compressed\scrcpy-win64-v3.3.1\scrcpy-win64-v3.3.1"
set "PATH=%SCRCPY_PATH%;%PATH%"

:: Test if scrcpy is accessible first
echo [*] Testing SCRCPY accessibility...
scrcpy --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] SCRCPY not found in PATH
    echo [*] Trying direct path...
    "%SCRCPY_PATH%\scrcpy.exe" --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] SCRCPY not found at: %SCRCPY_PATH%
        echo [*] Please check the path in the script
        pause
        exit /b
    )
)

:: Launch SCRCPY in minimized window (console minimized, G<PERSON> normal)
echo [*] Launching SCRCPY with minimized console...
if "%*"=="" (
    start /MIN "" "%SCRCPY_PATH%\scrcpy.exe"
) else (
    start /MIN "" "%SCRCPY_PATH%\scrcpy.exe" %*
)

echo [*] SCRCPY launched with minimized console window
echo [*] Phone screen should appear normally
echo [*] SCRCPY console is minimized in taskbar
echo [*] You can now use 'autoclose' to close this window
